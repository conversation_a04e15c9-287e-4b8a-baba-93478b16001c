[gd_scene load_steps=7 format=3 uid="uid://bu5b71kburkss"]

[ext_resource type="Script" uid="uid://bvlgjlfkax85t" path="res://src/virus.gd" id="1_5nuwd"]
[ext_resource type="Script" uid="uid://67hcr3h2ojbg" path="res://src/mass_based_scaler_component.gd" id="2_8fg0l"]
[ext_resource type="Texture2D" uid="uid://byog74s0lf1ha" path="res://src/units/purple/cell.png" id="2_8v58v"]
[ext_resource type="Script" uid="uid://c8akh72pe5wvb" path="res://src/combat_component.gd" id="3_8v58v"]
[ext_resource type="Script" uid="uid://f3sb32vkj9bl" path="res://src/mass_component.gd" id="4_eukup"]

[sub_resource type="CircleShape2D" id="CircleShape2D_56mx5"]
radius = 97.0681

[sub_resource type="CircleShape2D" id="CircleShape2D_8fg0l"]
radius = 110.202

[node name="Virus" type="CharacterBody2D" node_paths=PackedStringArray("combat_component", "scaler_component", "mass_component")]
scale = Vector2(0.3, 0.3)
collision_layer = 0
collision_mask = 0
motion_mode = 1
script = ExtResource("1_5nuwd")
combat_component = NodePath("CombatComponent")
speed = 3000.0
scaler_component = NodePath("MassBasedScalerComponent")
mass_component = NodePath("MassComponent")

[node name="MassComponent" type="Node" parent="."]
script = ExtResource("4_eukup")
metadata/_custom_type_script = "uid://f3sb32vkj9bl"

[node name="MassBasedScalerComponent" type="Node" parent="."]
script = ExtResource("2_8fg0l")
metadata/_custom_type_script = "uid://67hcr3h2ojbg"

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_8v58v")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_56mx5")

[node name="CombatComponent" type="Area2D" parent="." node_paths=PackedStringArray("combat_owner")]
collision_layer = 8
collision_mask = 3
script = ExtResource("3_8v58v")
combat_owner = NodePath("..")
metadata/_custom_type_script = "uid://c8akh72pe5wvb"

[node name="CollisionShape2D" type="CollisionShape2D" parent="CombatComponent"]
shape = SubResource("CircleShape2D_8fg0l")
