class_name <PERSON><PERSON><PERSON>
extends CharacterBody2D

enum State {
	IDLE,
	COLLECTING,
	RETURNING,
	ORBITING,
	CHASING,
	SEEKING,
	RECALLING
}

@export var combat_component: CombatComponent
@export var scaler_component: MassBasedScalerComponent
@export var sprite: Sprite2D

var type: UnitData.Type
var mass: float
var owner_node: MotherCell
var target: Node2D = null

var current_state: State

var speed: float = 150.0
var orbit_distance: float = 150.0
var orbit_angle: float = 0.0

func _ready() -> void:
	combat_component.combat_owner = self

func initialize(mother_cell: MotherCell, unit_data: UnitData) -> void:
	self.owner_node = mother_cell

	self.type = unit_data.unit_type
	self.mass = unit_data.cost
	sprite.texture = unit_data.texture

	scaler_component.initialize(self, unit_data.cost)
	scaler_component.update_scale(self.mass)

	combat_component.combat_owner = self

	var mother_cell_combat_component: CombatComponent = mother_cell.combat_component
	combat_component.collision_layer = mother_cell_combat_component.collision_layer
	combat_component.collision_mask = mother_cell_combat_component.collision_mask

	match type:
		UnitData.Type.COLLECTOR:
			_change_state(State.COLLECTING)
		UnitData.Type.DEFENDER:
			_change_state(State.ORBITING)
		UnitData.Type.HUNTER:
			_change_state(State.SEEKING)
		UnitData.Type.AGGRESSOR:
			var enemy_group: StringName = "player2_mother" if (owner_node as MotherCell).player_id == 1 else "player1_mother"
			var enemies: Array[Node] = get_tree().get_nodes_in_group(enemy_group)
			if not enemies.is_empty():
				target = enemies[0]
			_change_state(State.CHASING)

func _change_state(new_state: State) -> void:
	current_state = new_state
	if new_state == State.RETURNING or new_state == State.RECALLING:
		target = owner_node

func _physics_process(delta: float) -> void:
	if not is_instance_valid(owner_node):
		queue_free()
		return

	if mass <= 1:
		queue_free()

	if InputManager.is_action_pressed(owner_node.player_id, "recall"):
		if current_state != State.RECALLING:
			_change_state(State.RECALLING)

	match current_state:
		State.COLLECTING: _state_collecting(delta)
		State.RETURNING: _state_returning(delta)
		State.ORBITING: _state_orbiting(delta)
		State.CHASING: _state_chasing(delta)
		State.SEEKING: _state_seeking(delta)
		State.RECALLING: _state_recalling(delta)
		State.IDLE: _state_idle(delta)

	combat_component.process_combat(delta)
	move_and_slide()

func _state_idle(_delta: float) -> void:
	velocity = Vector2.ZERO

func _state_collecting(_delta: float) -> void:
	if not is_instance_valid(target):
		target = find_closest_in_group("food")

	if is_instance_valid(target):
		velocity = global_position.direction_to(target.global_position) * speed

		if global_position.distance_to(target.global_position) < 10:
			if target is Food:
				self.mass += (target as Food).mass_value
				scaler_component.update_scale(self.mass)
				target.queue_free()
				target = null
	else:
		_change_state(State.RETURNING)

func _state_returning(_delta: float) -> void:
	if not is_instance_valid(target):
		_change_state(State.IDLE)
		return

	velocity = global_position.direction_to(target.global_position) * speed
	if global_position.distance_to(target.global_position) < 30:
		owner_node.add_mass(self.mass)
		queue_free()

func _state_recalling(_delta: float) -> void:
	_state_returning(_delta)

func _state_orbiting(delta: float) -> void:
	target = find_closest_enemy_in_radius(orbit_distance)
	if is_instance_valid(target):
		_change_state(State.CHASING)
		return

	orbit_angle += 0.5 * delta
	var orbit_pos: Vector2 = owner_node.global_position + Vector2.RIGHT.rotated(orbit_angle) * orbit_distance
	var direction_to_orbit_pos: Vector2 = global_position.direction_to(orbit_pos)
	var desired_velocity: Vector2 = direction_to_orbit_pos * speed
	velocity = velocity.lerp(desired_velocity, delta * 5.0)

func _state_chasing(_delta: float) -> void:
	if not is_instance_valid(target):
		target = null
		match type:
			UnitData.Type.DEFENDER: _change_state(State.ORBITING)
			UnitData.Type.HUNTER: _change_state(State.SEEKING)
			UnitData.Type.AGGRESSOR:
				var enemy_group: StringName = "player2_mother" if owner_node.player_id == 1 else "player1_mother"
				var enemies: Array[Node] = get_tree().get_nodes_in_group(enemy_group)
				if not enemies.is_empty(): target = enemies[0]
				else: _change_state(State.IDLE)
		return

	velocity = global_position.direction_to(target.global_position) * speed

func _state_seeking(_delta: float) -> void:
	target = find_closest_enemy_in_radius(1000)
	if is_instance_valid(target):
		_change_state(State.CHASING)
		return

	velocity = transform.x * speed

func find_closest_in_group(group_name: String) -> Node2D:
	return find_closest(get_tree().get_nodes_in_group(group_name))

func find_closest_enemy_in_radius(radius: float) -> Node2D:
	var enemy_groups: Array = ["player2", "virus"] if owner_node.player_id == 1 else ["player1", "virus"]
	var potential_targets: Array[Node] = []
	for group: StringName in enemy_groups:
		potential_targets.append_array(get_tree().get_nodes_in_group(group))
	return find_closest(potential_targets, radius)

func find_closest(nodes: Array, max_dist: float = INF) -> Node2D:
	var closest: Node2D = null
	var min_dist_sq: float = max_dist * max_dist
	for node: Node2D in nodes:
		var dist_sq: float = global_position.distance_squared_to(node.global_position)
		if dist_sq < min_dist_sq:
			min_dist_sq = dist_sq
			closest = node
	return closest
